package it.masterzen.Dungeon;

import com.earth2me.essentials.commands.WarpNotFoundException;
import com.sk89q.worldedit.MaxChangedBlocksException;
import com.sk89q.worldedit.Vector;
import com.sk89q.worldedit.world.DataException;
import com.sk89q.worldguard.bukkit.RegionContainer;
import com.sk89q.worldguard.protection.ApplicableRegionSet;
import com.sk89q.worldguard.protection.flags.DefaultFlag;
import com.sk89q.worldguard.protection.flags.Flags;
import com.sk89q.worldguard.protection.flags.StateFlag;
import com.sk89q.worldguard.protection.managers.RegionManager;
import it.masterzen.Giveaway.Giveaway;
import it.masterzen.MongoDB.PlayerData;
import it.masterzen.Robot.Robot;
import it.masterzen.blockbreak.AlphaBlockBreak;
import it.masterzen.blockbreak.XMaterial;
import net.ess3.api.InvalidWorldException;
import net.md_5.bungee.api.ChatMessageType;
import net.md_5.bungee.api.chat.TextComponent;
import org.bukkit.*;
import org.bukkit.attribute.Attribute;
import org.bukkit.block.Block;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.configuration.InvalidConfigurationException;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.*;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntityDeathEvent;
import org.bukkit.event.player.PlayerMoveEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitRunnable;

import java.io.File;
import java.io.IOException;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

public class Main implements CommandExecutor, Listener {

    private static HashMap<UUID, Crystals> points = new HashMap<>();
    private final String prefix = "§e§lDUNGEON §8»§7 ";
    private final String name = "Crystals";
    private static YamlConfiguration ymlFile;

    public final AlphaBlockBreak mainClass;
    private it.masterzen.Dungeon.GUI gui;
    private it.masterzen.Keys.Main keyManager;
    private List<Location> blockOnCooldown = new ArrayList<>();
    private List<ArmorStand> floatingTextList = new ArrayList<>();
    private final List<Material> allowedBlocks = new ArrayList<>();

    private final List<ItemStack> swordList = new ArrayList<>();
    private final List<ItemStack> helmetList = new ArrayList<>();
    private final List<ItemStack> chestplateList = new ArrayList<>();
    private final List<ItemStack> leggingsList = new ArrayList<>();
    private final List<ItemStack> bootsList = new ArrayList<>();

    private List<UUID> oneMinuteScheduler = new ArrayList<>();
    private List<UUID> fiveMinuteScheduler = new ArrayList<>();
    private List<Location> temporaryOres = new ArrayList<>();

    public Main(AlphaBlockBreak plugin) {
        mainClass = plugin;
        gui = new GUI(this);
        keyManager = mainClass.getKeysManager();
        setupAllowedBlocks();
        setupGear();
    }

    public void setupGear() {
        swordList.add(new ItemStack(Material.WOOD_SWORD));
        swordList.add(new ItemStack(Material.STONE_SWORD));
        swordList.add(new ItemStack(Material.IRON_SWORD));
        swordList.add(new ItemStack(Material.GOLD_SWORD));
        swordList.add(new ItemStack(Material.DIAMOND_SWORD));

        helmetList.add(new ItemStack(Material.LEATHER_HELMET));
        helmetList.add(new ItemStack(Material.CHAINMAIL_HELMET));
        helmetList.add(new ItemStack(Material.IRON_HELMET));
        helmetList.add(new ItemStack(Material.GOLD_HELMET));
        helmetList.add(new ItemStack(Material.DIAMOND_HELMET));

        chestplateList.add(new ItemStack(Material.LEATHER_CHESTPLATE));
        chestplateList.add(new ItemStack(Material.CHAINMAIL_CHESTPLATE));
        chestplateList.add(new ItemStack(Material.IRON_CHESTPLATE));
        chestplateList.add(new ItemStack(Material.GOLD_CHESTPLATE));
        chestplateList.add(new ItemStack(Material.DIAMOND_CHESTPLATE));

        leggingsList.add(new ItemStack(Material.LEATHER_LEGGINGS));
        leggingsList.add(new ItemStack(Material.CHAINMAIL_LEGGINGS));
        leggingsList.add(new ItemStack(Material.IRON_LEGGINGS));
        leggingsList.add(new ItemStack(Material.GOLD_LEGGINGS));
        leggingsList.add(new ItemStack(Material.DIAMOND_LEGGINGS));

        bootsList.add(new ItemStack(Material.LEATHER_BOOTS));
        bootsList.add(new ItemStack(Material.CHAINMAIL_BOOTS));
        bootsList.add(new ItemStack(Material.IRON_BOOTS));
        bootsList.add(new ItemStack(Material.GOLD_BOOTS));
        bootsList.add(new ItemStack(Material.DIAMOND_BOOTS));
    }

    public void setupAllowedBlocks() {
        allowedBlocks.add(Material.COAL_ORE);
        allowedBlocks.add(Material.IRON_ORE);
        allowedBlocks.add(Material.DIAMOND_ORE);
        allowedBlocks.add(Material.EMERALD_ORE);
    }

    public void resetAllBlockOnCooldown() {
        for (Location location : blockOnCooldown) {
            location.getBlock().setType(Material.COAL_ORE);
        }
        for (Location location : temporaryOres) {
            location.getBlock().setType(Material.STONE);
        }
        for (ArmorStand armorStand : floatingTextList) {
            armorStand.remove();
        }
    }

    public List<Location> getRandom(Location loc, int radius) {
        List<Location> range = new ArrayList <>();

        World w = loc.getWorld();
        int cx = loc.getBlockX();
        int cz = loc.getBlockZ();
        int rs = radius * radius;

        for (int x = cx - radius; x <= cx + radius; x++) {
            for (int z = cz - radius; z <= cz + radius; z++) {
                //if ((cx - x) * (cx - x) + (cz - z) * (cz - z) <= rs) {
                    Location rloc = new Location(w, x, loc.getY(), z);

                    if (!w.getBlockAt(rloc).getType().isSolid()) {
                        range.add(rloc);
                    }
                //}
            }
        }

        return range;
    }

    @EventHandler
    public void checkWhoDie(EntityDeathEvent event) {
        if (event.getEntity() instanceof Zombie && event.getEntity().getKiller() instanceof Player) {
            Entity death = event.getEntity();
            Player player = event.getEntity().getKiller();

            Location eventLocation = death.getLocation();
            Vector v = new Vector(eventLocation.getX(), eventLocation.getBlockY(), eventLocation.getZ());
            List<String> RegionName = mainClass.getWorldGuard().getRegionManager(eventLocation.getWorld()).getApplicableRegionsIDs(v);
            String regionnametmp = RegionName.toString().replace("[", "").replace("]", "");

            if (regionnametmp.contains("dungeon") || regionnametmp.contains("dungeon2")) {
                event.getDrops().clear();
                event.setDroppedExp(0);
                keyManager.giveRandomKeys(player, 3, 1);
                addPoints(player, 1, 5);
                player.spigot().sendMessage(ChatMessageType.ACTION_BAR, TextComponent.fromLegacyText("§a+ 5 §7Tier 1 Crystals"));
            }
        }
    }

    @EventHandler
    public void onPlayerHitting(EntityDamageByEntityEvent event) {
        if (event.getDamager() instanceof Player) {
            if (event.getEntity() instanceof Zombie) {
                Player player = (Player) event.getDamager();
                Zombie zombie = (Zombie) event.getEntity();

                NumberFormat formatter = new DecimalFormat("#,###.##");
                zombie.setCustomName("§c§lVILLAIN §7§o(§a§o" + formatter.format(zombie.getHealth()) + "§7§o/§a§o" + formatter.format(zombie.getAttribute(Attribute.GENERIC_MAX_HEALTH).getValue()) + "§7§o)");
            }
        }
    }

    public void spawnZombieAroundPlayer(Player player) {
        Location playerLocation = player.getLocation().clone();
        List<Location> locationList = getRandom(playerLocation, 2);
        boolean spawned = false;

        for (Location zombieLocation : locationList) {
            if (!spawned) {
                Zombie zombie = player.getWorld().spawn(zombieLocation, Zombie.class);

                int canBeBaby = ThreadLocalRandom.current().nextInt(2);
                if (canBeBaby == 1) {
                    zombie.setBaby(true);
                }

                zombie.getEquipment().setHelmet(helmetList.get(ThreadLocalRandom.current().nextInt(helmetList.size() - 1)));
                zombie.getEquipment().setChestplate(chestplateList.get(ThreadLocalRandom.current().nextInt(chestplateList.size() - 1)));
                zombie.getEquipment().setLeggings(leggingsList.get(ThreadLocalRandom.current().nextInt(leggingsList.size() - 1)));
                zombie.getEquipment().setBoots(bootsList.get(ThreadLocalRandom.current().nextInt(bootsList.size() - 1)));
                zombie.getEquipment().setItemInMainHand(swordList.get(ThreadLocalRandom.current().nextInt(swordList.size() - 1)));

                zombie.setCanPickupItems(false);
                NumberFormat formatter = new DecimalFormat("#,###.##");
                zombie.setCustomName("§c§lVILLAIN §7§o(§a§o" + formatter.format(zombie.getHealth()) + "§7§o/§a§o" + formatter.format(zombie.getAttribute(Attribute.GENERIC_MAX_HEALTH).getValue()) + "§7§o)");
                zombie.setCustomNameVisible(true);

                if (!zombie.isBaby()) {
                    zombie.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, 9999, 1, false));
                }
                zombie.addPotionEffect(new PotionEffect(PotionEffectType.FIRE_RESISTANCE, 9999, 1, false));
                zombie.addPotionEffect(new PotionEffect(PotionEffectType.INCREASE_DAMAGE, 9999, 2, false));

                spawned = true;
            }
        }
    }

    public void killMobs(Player player, int radius) {
        int totalMobsKilled = 0;

        for (Entity entity : player.getNearbyEntities(radius, radius, radius)) {
            if (entity instanceof Zombie) {
                totalMobsKilled++;
                entity.remove();
            }
        }

        if (totalMobsKilled > 0) {
            int killerLevel = (int) mainClass.getEnchantLevel(player, "Dungeon Killer");
            keyManager.giveRandomKeys(player, 2 * totalMobsKilled, killerLevel);
            addPoints(player, 1, (5L * totalMobsKilled * killerLevel));
            player.spigot().sendMessage(ChatMessageType.ACTION_BAR, TextComponent.fromLegacyText("§a+ " + (5L * totalMobsKilled * killerLevel) + " §7Tier 1 Crystals" + (killerLevel > 1 ? "§7§o(" + killerLevel + "x)" : "")));
            if (!player.hasPermission("dungeonkiller.remove")) {
                player.sendMessage(prefix + "The §c§lKILLER§7 one shot a total of §c§l" + totalMobsKilled + "§7 Villains");
            }
        } else {
            if (!player.hasPermission("dungeonkiller.remove")) {
                player.sendMessage(prefix + "There was no Villains to kill");
            }
        }
    }

    public void bombEffect(Player player, int radius) {
        Location eventLocation = player.getLocation();
        Vector v = new Vector(eventLocation.getX(), eventLocation.getBlockY(), eventLocation.getZ());
        List<String> RegionName = mainClass.getWorldGuard().getRegionManager(eventLocation.getWorld()).getApplicableRegionsIDs(v);
        String regionnametmp = RegionName.toString().replace("[", "").replace("]", "");

        if (regionnametmp.contains("dungeon")) {
            Location loc = player.getLocation();
            World world = player.getWorld();
            int totalBlocks = 0;
            List<Location> temporaryBlocks = new ArrayList<>();

            for (int x = loc.getBlockX() - radius; x < loc.getBlockX() + radius; x++) {
                for (int y = loc.getBlockY() - radius; y < loc.getBlockY() + radius; y++) {
                    for (int z = loc.getBlockZ() - radius; z < loc.getBlockZ() + radius; z++) {
                        Block block = world.getBlockAt(x, y, z);
                        if (block.getType().equals(Material.STONE)) {
                            block.setType(getRandomMaterial());
                            if (!temporaryOres.contains(block.getLocation())) {
                                temporaryOres.add(block.getLocation());
                            }
                            if (!temporaryBlocks.contains(block.getLocation())) {
                                temporaryBlocks.add(block.getLocation());
                            }
                            totalBlocks++;
                        }
                    }
                }
            }

            if (!player.hasPermission("dungeonbomb.remove")) {
                player.sendMessage(prefix + "Your §c§lBOMB §7transform a total of §c§l" + totalBlocks + "§7 blocks into ORES !");
            }

            new BukkitRunnable() {
                @Override
                public void run() {
                    for (Location location : temporaryBlocks) {
                        location.getBlock().setType(Material.STONE);
                    }
                }
            }.runTaskLater(mainClass, 1200L);
        }
    }

    public Material getRandomMaterial() {
        int chance = ThreadLocalRandom.current().nextInt(100);
        Material randomMaterial = Material.AIR;

        if (chance <= 1) {
            randomMaterial = Material.EMERALD_ORE;
        } else if (chance <= 5) {
            randomMaterial = Material.DIAMOND_ORE;
        } else if (chance <= 20) {
            randomMaterial= Material.IRON_ORE;
        } else {
            randomMaterial = Material.COAL_ORE;
        }

        return randomMaterial;
    }

    @EventHandler
    public void onPlayerMove(PlayerMoveEvent event) {
        Player player = event.getPlayer();
        Location eventLocation = player.getLocation();
        Vector v = new Vector(eventLocation.getX(), eventLocation.getBlockY(), eventLocation.getZ());
        List<String> RegionName = mainClass.getWorldGuard().getRegionManager(eventLocation.getWorld()).getApplicableRegionsIDs(v);
        String regionnametmp = RegionName.toString().replace("[", "").replace("]", "");

        if (regionnametmp.contains("dungeon")) {
            if (!player.isOp() && player.isFlying()) {
                player.setFlying(false);
                player.sendMessage(prefix + "Fly has been turned §c§lOFF");
            }

            int chance = ThreadLocalRandom.current().nextInt(1000);
            if (chance <= 1) {
                if (!event.getTo().equals(event.getFrom())) {
                    spawnZombieAroundPlayer(player);
                }
            }

            if (!player.hasPermission("dungeonkiller.disable")) {
                new BukkitRunnable() {
                    @Override
                    public void run() {
                        if (!oneMinuteScheduler.contains(player.getUniqueId())) {
                            if (mainClass.hasEnchant(player, "Dungeon Killer")) {
                                oneMinuteScheduler.add(player.getUniqueId());
                                mainClass.addPex(player, "dungeonkiller.cooldown", 60, true);
                                new BukkitRunnable() {
                                    @Override
                                    public void run() {
                                        killMobs(player, 5);
                                        oneMinuteScheduler.remove(player.getUniqueId());
                                    }
                                }.runTaskLater(mainClass, 1200L);
                            }
                        }
                    }
                }.runTask(mainClass);
            }

            if (!player.hasPermission("dungeonbomb.disable")) {
                new BukkitRunnable() {
                    @Override
                    public void run() {
                        if (!fiveMinuteScheduler.contains(player.getUniqueId())) {
                            if (mainClass.hasEnchant(player, "Dungeon Bomb")) {
                                fiveMinuteScheduler.add(player.getUniqueId());
                                mainClass.addPex(player, "dungeonbomb.cooldown", 300, true);
                                new BukkitRunnable() {
                                    @Override
                                    public void run() {
                                        bombEffect(player, 5);
                                        fiveMinuteScheduler.remove(player.getUniqueId());
                                    }
                                }.runTaskLater(mainClass, 6000L);
                            }
                        }
                    }
                }.runTask(mainClass);
            }
        }
    }

    @EventHandler
    public void blockbreak(BlockBreakEvent event) throws MaxChangedBlocksException, IOException, DataException, InvalidConfigurationException {
        event.setDropItems(false);
        Player player = event.getPlayer();

        if (player.getWorld().getName().equalsIgnoreCase("Build")) {
            ItemStack stack = new ItemStack(event.getBlock().getType(), 1, event.getBlock().getData());
            ApplicableRegionSet set = mainClass.getWorldGuard().getRegionManager(player.getWorld()).getApplicableRegions(event.getBlock().getLocation());

            if (allowedBlocks.contains(stack.getType())) {
                Location eventLocation = event.getBlock().getLocation();
                Vector v = new Vector(eventLocation.getX(), eventLocation.getBlockY(), eventLocation.getZ());
                List<String> RegionName = mainClass.getWorldGuard().getRegionManager(eventLocation.getWorld()).getApplicableRegionsIDs(v);
                String regionnametmp = RegionName.toString().replace("[", "").replace("]", "");

                if (regionnametmp.contains("dungeon")) {
                    event.setCancelled(true);

                    // Quest System
                    mainClass.getQuestManager().addProgress(player, "DUNGEON_MINER", 1);

                    // Discount System
                    int chance = ThreadLocalRandom.current().nextInt(150);
                    if (chance <= 1) {
                        PlayerData data = mainClass.getMongoReader().getPlayerData(player.getUniqueId());
                        if (data.getDungeonDiscount() == null) {
                            data.setDungeonDiscount(0);
                        }
                        if (data.getDungeonDiscount() < 200) {
                            data.setDungeonDiscount(data.getDungeonDiscount() + 1);
                            player.sendMessage(prefix + "You received a point discount for Random Spawner on /dungeon shop");
                        }
                    }

                    String tmp;
                    int fortuneLevel = 0;
                    int luckLevel = 0;

                    if (player.getInventory().getItemInMainHand().hasItemMeta() && player.getInventory().getItemInMainHand().getItemMeta().hasLore()) {
                        List<String> lore = player.getInventory().getItemInMainHand().getItemMeta().getLore();
                        for (String line : lore) {
                            if (line.contains("§b▍ §7Dungeon Fortune")) {
                                tmp = line.replace("§b▍ §7Dungeon Fortune ", "");
                                tmp = ChatColor.stripColor(tmp);
                                fortuneLevel = Integer.parseInt(tmp);
                            } else if (line.contains("§b▍ §7Dungeon Luck")) {
                                tmp = line.replace("§b▍ §7Dungeon Luck ", "");
                                tmp = ChatColor.stripColor(tmp);
                                luckLevel = Integer.parseInt(tmp);
                            }
                        }
                    }

                    ArmorStand floatingText = (ArmorStand) event.getBlock().getWorld().spawnEntity(event.getBlock().getLocation().clone().add(0.5, -1.25, 0.5), EntityType.ARMOR_STAND);

                    // 17/07/2022 DOPPI CRISTALLI
                    int totalCrystals = 2;
                    chance = ThreadLocalRandom.current().nextInt(3) + 1;
                    if (!player.hasPermission("dungeonfortune.disable")) {
                        if (chance <= fortuneLevel) {
                            totalCrystals = totalCrystals * 2;
                        }
                    }

                    if (event.getBlock().getType().equals(Material.COAL_ORE)) {
                        player.spigot().sendMessage(ChatMessageType.ACTION_BAR, TextComponent.fromLegacyText("§a+ " + totalCrystals + " §7Tier 1 Crystal"));
                        floatingText.setCustomName("§a§l+ " + totalCrystals + " §7Tier 1");
                        addPoints(player, 1, totalCrystals);
                    } else if (event.getBlock().getType().equals(Material.IRON_ORE)) {
                        totalCrystals = totalCrystals * 2;
                        player.spigot().sendMessage(ChatMessageType.ACTION_BAR, TextComponent.fromLegacyText("§a+ " + totalCrystals + " §7Tier 1 Crystals"));
                        floatingText.setCustomName("§a§l+ " + totalCrystals + " §7Tier 1");
                        addPoints(player, 1, totalCrystals);
                    } else if (event.getBlock().getType().equals(Material.DIAMOND_ORE)) {
                        player.spigot().sendMessage(ChatMessageType.ACTION_BAR, TextComponent.fromLegacyText("§a+ " + totalCrystals + " §7Tier 2 Crystal"));
                        floatingText.setCustomName("§a§l+ " + totalCrystals + " §7Tier 2");
                        addPoints(player, 2, totalCrystals);
                    } else if (event.getBlock().getType().equals(Material.EMERALD_ORE)) {
                        player.spigot().sendMessage(ChatMessageType.ACTION_BAR, TextComponent.fromLegacyText("§a+ " + totalCrystals + " §7Tier 3 Crystal"));
                        floatingText.setCustomName("§a§l+ " + totalCrystals + " §7Tier 3");
                        addPoints(player, 3, totalCrystals);
                    }
                    floatingText.setVisible(false);
                    floatingText.setCustomNameVisible(true);
                    floatingText.setGravity(false);
                    floatingTextList.add(floatingText);

                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            floatingText.remove();
                        }
                    }.runTaskLater(mainClass, 80L);

                    chance = ThreadLocalRandom.current().nextInt(5);
                    if (chance == 1) {
                        keyManager.giveRandomKeys(player, 2, 1);
                    }
                    chance = ThreadLocalRandom.current().nextInt(1500);
                    if (chance == 1) {
                        keyManager.giveKeys(player, "Armor", 1, true);
                        Bukkit.broadcastMessage(prefix + player.getName() + " found 1x " + keyManager.getKeyName("Armor") + " §7Key from Dungeon");
                    }
                    if (temporaryOres.contains(eventLocation)) {
                        event.getBlock().setType(Material.STONE);
                        temporaryOres.remove(eventLocation);
                    } else {
                        event.getBlock().setType(Material.BEDROCK);
                        blockOnCooldown.add(eventLocation);
                        int finalLuckLevel = luckLevel;
                        new BukkitRunnable() {
                            @Override
                            public void run() {
                                int chance = ThreadLocalRandom.current().nextInt(100);
                                if (chance <= 1 || (chance <= (finalLuckLevel * 3 + 1) && !player.hasPermission("dungeonluck.disable"))) {
                                    if (chance > 1 && !player.hasPermission("dungeonluck.remove")) {
                                        player.sendMessage(prefix + "You had luck and the block got boosted to §a§lEMERALD");
                                    }
                                    event.getBlock().setType(Material.EMERALD_ORE);
                                } else if (chance <= 5 || (chance <= (finalLuckLevel * 4 + 5) && !player.hasPermission("dungeonluck.disable"))) {
                                    if (chance > 5 && !player.hasPermission("dungeonluck.remove")) {
                                        player.sendMessage(prefix + "You had luck and the block got boosted to §b§lDIAMOND");
                                    }
                                    event.getBlock().setType(Material.DIAMOND_ORE);
                                } else if (chance <= 20 || (chance <= (finalLuckLevel * 5 + 20) && !player.hasPermission("dungeonluck.disable"))) {
                                    if (chance > 20 && !player.hasPermission("dungeonluck.remove")) {
                                        player.sendMessage(prefix + "You had luck and the block got boosted to §f§lIRON");
                                    }
                                    event.getBlock().setType(Material.IRON_ORE);
                                } else {
                                    event.getBlock().setType(Material.COAL_ORE);
                                }
                                blockOnCooldown.remove(eventLocation);
                            }
                        }.runTaskLater(mainClass, 100L);
                    }
                }
            } else if (!player.isOp()) {
                event.setCancelled(true);
            }
        }
    }

    public String getPrefix() {
        return this.prefix;
    }

    public String getName() {
        return this.name;
    }

    public void loadPoints() {
        for (Player player : Bukkit.getOnlinePlayers()) {
            loadPoints(player);
        }
    }

    public void loadPoints(Player player) {
        File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/Player" + name + ".yml");
        if (!file.exists()) {
            try {
                file.createNewFile();
            } catch (Exception ex) {
            }
        }

        ymlFile = YamlConfiguration.loadConfiguration(file);
        if (!points.containsKey(player.getUniqueId())) {
            points.put(player.getUniqueId(), new Crystals(ymlFile.getLong(player.getUniqueId() + ".tier1"), ymlFile.getLong(player.getUniqueId() + ".tier2"), ymlFile.getLong(player.getUniqueId() + ".tier3")));
        }
    }

    public HashMap<UUID, Crystals> getPoints() {
        return points;
    }

    public void savePoints() throws IOException {
        File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/Player" + name + ".yml");
        if (!file.exists()) {
            try {
                file.createNewFile();
            } catch (Exception ex) {
            }
        }

        ymlFile = YamlConfiguration.loadConfiguration(file);
        if (!points.isEmpty() && ymlFile != null) {
            for (UUID player : points.keySet()) {
                savePoints(player, false);
            }
        }
    }

    public void savePoints(Player player, boolean remove) throws IOException {
        if (!points.isEmpty() && points.containsKey(player.getUniqueId())) {
            File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/Player" + name + ".yml");
            ymlFile = YamlConfiguration.loadConfiguration(file);
            ymlFile.set(player.getUniqueId() + ".tier1", points.get(player.getUniqueId()).getTier1());
            ymlFile.set(player.getUniqueId() + ".tier2", points.get(player.getUniqueId()).getTier2());
            ymlFile.set(player.getUniqueId() + ".tier3", points.get(player.getUniqueId()).getTier3());
            ymlFile.save(file);
            if (remove) {
                points.remove(player.getUniqueId());
            }
        }
    }

    public void savePoints(UUID player, boolean remove) throws IOException {
        if (!points.isEmpty() && points.containsKey(player)) {
            File file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/Player" + name + ".yml");
            ymlFile = YamlConfiguration.loadConfiguration(file);
            ymlFile.set(player.toString() + ".tier1", points.get(player).getTier1());
            ymlFile.set(player.toString() + ".tier2", points.get(player).getTier2());
            ymlFile.set(player.toString() + ".tier3", points.get(player).getTier3());
            ymlFile.save(file);
            if (remove) {
                points.remove(player);
            }
        }
    }

    public void addPoints(Player player, int tier, long amount) {
        if (!points.containsKey(player.getUniqueId())) {
            points.put(player.getUniqueId(), new Crystals(0, 0, 0));
        }

        // Apply dungeon skill multiplier
        long finalAmount = applyDungeonSkillMultiplier(player, amount);
        points.get(player.getUniqueId()).addCrystals(tier, finalAmount);
    }

    private long applyDungeonSkillMultiplier(Player player, long baseAmount) {
        if (mainClass.getPlayerSkillsLevel().containsKey(player.getUniqueId())) {
            int dungeonSkillLevel = mainClass.getPlayerSkillsLevel().get(player.getUniqueId()).getDungeon();
            if (dungeonSkillLevel > 0) {
                // Level 1: 50% more (1.5x), Level 2: 100% more (2.0x)
                double multiplier = 1.0 + (dungeonSkillLevel * 0.5);
                return Math.round(baseAmount * multiplier);
            }
        }
        return baseAmount;
    }

    public void removePoints(Player player, int tier, long amount) {
        if (points.containsKey(player.getUniqueId())) {
            points.get(player.getUniqueId()).removeCrystals(tier, amount);
        }
    }

    public void setPoints(Player player, int tier, long amount) {
        if (points.containsKey(player.getUniqueId())) {
            points.get(player.getUniqueId()).setCrystals(tier, amount);
        }
    }

    public void sendPoints(Player player) {
        if (!points.containsKey(player.getUniqueId())) {
            player.sendMessage(prefix + "You don't have any " + name + ". You can get them from mining at /dungeon");
        } else {
            player.sendMessage("§e§l" + name.toUpperCase() + " §f| §7 Balance");
            player.sendMessage("");
            player.sendMessage("§7You have §a§l" + points.get(player.getUniqueId()).getTier1() + " §7Tier 1 " + name);
            player.sendMessage("§7You have §a§l" + points.get(player.getUniqueId()).getTier2() + " §7Tier 2 " + name);
            player.sendMessage("§7You have §a§l" + points.get(player.getUniqueId()).getTier3() + " §7Tier 3 " + name);
        }
    }

    public long getPoints(Player player, int tier) {
        if (!points.containsKey(player.getUniqueId())) {
            return 0;
        } else {
            long point = 0;
            if (tier == 1) {
                point = points.get(player.getUniqueId()).getTier1();
            } else if (tier == 2) {
                point = points.get(player.getUniqueId()).getTier2();
            } else if (tier == 3) {
                point = points.get(player.getUniqueId()).getTier3();
            }
            return point;
        }
    }

    @Override
    public boolean onCommand(CommandSender sender, Command cmd, String label, String[] args) {
        if (cmd.getName().equalsIgnoreCase("dungeon")) {
            if (sender instanceof Player) {
                Player player = (Player) sender;
                if (player.isOp() && args.length == 4 && args[0].equalsIgnoreCase("give")) {
                    if (Bukkit.getPlayerExact(args[1]) != null) {
                        Player tmpPlayer = Bukkit.getPlayerExact(args[1]);
                        int tier = Integer.parseInt(args[2]);
                        long points = Long.parseLong(args[3]);
                        addPoints(tmpPlayer, tier, points);
                        player.sendMessage(prefix + "§a§l" + points + " §7Tier " + tier + " " + name + " §7Added to " + tmpPlayer.getName() + "'s balance §7§o(" + getPoints(tmpPlayer, tier) + ")");
                        if (!player.getName().equals(tmpPlayer.getName())) {
                            tmpPlayer.sendMessage(prefix + "You received §a§l" + points + " §7Tier " + tier + " " + name);
                        }
                    } else {
                        player.sendMessage(prefix + "§cPlayer offline");
                    }
                } else if (player.isOp() && args.length == 3 && args[0].equalsIgnoreCase("reset")) {
                    Player tmpPlayer = Bukkit.getPlayerExact(args[1]);
                    int tier = Integer.parseInt(args[2]);
                    removePoints(tmpPlayer, tier, getPoints(tmpPlayer, tier));
                    tmpPlayer.sendMessage(prefix + "Your Tier " + tier + " " + name + " balance has been resetted");
                    player.sendMessage(prefix + "You succesfully reset " + tmpPlayer.getName() + "'s Tier " + tier + " " + name + " balance");
                } else if (player.isOp() && args.length == 1 && args[0].equalsIgnoreCase("killer")) {
                    killMobs(player, 5);
                } else if (player.isOp() && args.length == 1 && args[0].equalsIgnoreCase("bomb")) {
                    bombEffect(player, 5);
                } else if (args.length == 1 && (args[0].equalsIgnoreCase("bal") || args[0].equalsIgnoreCase("balance"))) {
                    sendPoints(player);
                } else if (args.length == 1 && args[0].equalsIgnoreCase("shop")) {
                    gui.openGUI(player);
                } else {
                    try {
                        player.teleport(mainClass.getEssentials().getWarps().getWarp("dungeon"));
                    } catch (WarpNotFoundException | InvalidWorldException e) {
                        e.printStackTrace();
                    }
                }
            } else {
                if (args.length == 4 && args[0].equalsIgnoreCase("give")) {
                    if (Bukkit.getPlayerExact(args[1]) != null) {
                        Player tmpPlayer = Bukkit.getPlayerExact(args[1]);
                        int tier = Integer.parseInt(args[2]);
                        long points = Long.parseLong(args[3]);
                        addPoints(tmpPlayer, tier, points);
                    }
                } else if (args.length == 3 && args[0].equalsIgnoreCase("reset")) {
                    Player tmpPlayer = Bukkit.getPlayerExact(args[1]);
                    int tier = Integer.parseInt(args[2]);
                    removePoints(tmpPlayer, tier, getPoints(tmpPlayer, tier));
                    tmpPlayer.sendMessage(prefix + "Your Tier " + tier + " " + name + " balance has been resetted");
                }
            }
        }
        return false;
    }
}
