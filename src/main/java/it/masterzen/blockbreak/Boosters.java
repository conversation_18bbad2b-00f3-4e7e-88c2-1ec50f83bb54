package it.masterzen.blockbreak;

import it.masterzen.CustomArmor.PerkItem;
import it.masterzen.MongoDB.PlayerData;
import it.masterzen.SkillTree.PlayerSkill;
import it.masterzen.commands.Main;
import net.luckperms.api.model.user.User;
import net.luckperms.api.node.Node;
import org.apache.commons.lang3.StringUtils;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.scheduler.BukkitRunnable;

import java.text.DecimalFormat;
import java.util.*;

public class Boosters {

    private final String prefix = "§e§lBOOSTERS §8»§7 ";
    public final AlphaBlockBreak mainClass;

    public Boosters(AlphaBlockBreak plugin) {
        mainClass = plugin;
    }

    public void openMenu(Player player) {
        new BukkitRunnable() {
            @Override
            public void run() {
                Inventory gui = Bukkit.createInventory(null, 27, "§e§lENCHANT §f| §7Menu");
                Main.FillBorder(gui);

                PlayerData data = mainClass.getMongoReader().getPlayerData(player.getUniqueId());
                ItemStack boosters = new ItemStack(Material.GOLD_INGOT);
                ItemMeta meta = boosters.getItemMeta();
                meta.setDisplayName("§c§lPERSONAL BOOSTERS");
                List<String> lore = new ArrayList<>();
                lore.add("");
                // RANK
                if (!StringUtils.equalsIgnoreCase(mainClass.getPlayerVip(player), "none")) {
                    double booster = 0D;
                    if (player.hasPermission("essentials.warps.Alpha")) {
                        booster = 1.25;
                    } else if (player.hasPermission("essentials.warps.Mvp+")) {
                        booster = 1.2;
                    } else if (player.hasPermission("essentials.warps.Mvp")) {
                        booster = 1.15;
                    } else if (player.hasPermission("essentials.warps.Vip+")) {
                        booster = 1.1;
                    } else if (player.hasPermission("essentials.warps.Vip")) {
                        booster = 1.05;
                    }

                    lore.add("§e§lRANK");
                    lore.add("§e| §fMoney Booster: §ex" + booster);
                    lore.add("");
                } else {
                    lore.add("§e§lRANK");
                    lore.add("§e| §fNo boosters from your current rank");
                    lore.add("");
                }
                // SKILLS
                if (mainClass.getPlayerSkillsLevel().containsKey(player.getUniqueId())) {
                    PlayerSkill playerSkill = mainClass.getPlayerSkillsLevel().get(player.getUniqueId());
                    lore.add("§a§lSKILLS §7§o(/skills)");
                    lore.add("§a| §fMoney Booster: §a" + (playerSkill.getMoney() * 10) + "%");
                    lore.add("§a| §fToken Booster: §a" + (playerSkill.getToken() * 10) + "%");
                    lore.add("§a| §fXP Booster: §a" + (playerSkill.getXp() * 10) + "%");
                    lore.add("§a| §fKeys Booster: §a" + playerSkill.getKeys() + " (higher proc rate)");
                    lore.add("§a| §fDungeon Crystal Booster: §a" + (playerSkill.getDungeon() * 50) + "%");
                    lore.add("§a| §fJackHammer Token Finder: §a" + (mainClass.hasPermissionFromLuckPerms(player, "skills.JACKHAMMER.1") ? "UNLOCKED" : "§cLOCKED"));
                    lore.add("");
                } else {
                    lore.add("§a§lSKILLS §7§o(/skills)");
                    lore.add("§a| §fYou don't have any booster from /skill");
                    lore.add("");
                }
                // ARMORS
                if (!mainClass.getArmorSystem().getPlayerPerks(player).isEmpty()) {
                    List<PerkItem> perks = mainClass.getArmorSystem().getPlayerPerks(player);
                    lore.add("§6§lARMORS §7§o(/armor)");

                    Map<String, Double> perkMap = new HashMap<>();
                    for (PerkItem perk : perks) {
                        if (!perkMap.containsKey(perk.getType())) {
                            perkMap.put(perk.getType(), 0D);
                        }
                        perkMap.put(perk.getType(), perkMap.get(perk.getType()) + perk.getBoost());
                    }

                    for (String perk : perkMap.keySet()) {
                        lore.add("§6| §f" + perk + " Booster: §6" + perkMap.get(perk) + "%");
                    }
                    lore.add("");
                } else {
                    lore.add("§6§lARMORS §7§o(/armor)");
                    lore.add("§6| §fYou don't have any booster from /armor");
                    lore.add("");
                }
                // XPSHOP
                if (data.getXpShopBooster() != null && data.getXpShopBooster() > 0) {
                    lore.add("§b§lXPSHOP §7§o(/xpshop)");
                    lore.add("§b| §fXP Booster: §b" + data.getXpShopBooster() + "%");
                    lore.add("");
                } else {
                    lore.add("§b§lXPSHOP §7§o(/xpshop)");
                    lore.add("§b| §fYou don't have any booster from /xpshop");
                    lore.add("");
                }
                // MILESTONES
                if ((data.getMoneyBoosterFromMilestone() != null && data.getMoneyBoosterFromMilestone() > 0)
                        || (data.getTokenBoosterFromMilestone() != null && data.getTokenBoosterFromMilestone() > 0)
                        || (data.getKeysBoosterFromMilestone() != null && data.getKeysBoosterFromMilestone() > 0)
                        || (data.getXpBoosterFromMilestone() != null && data.getXpBoosterFromMilestone() > 0)) {
                    lore.add("§c§lMILESTONES §7§o(/milestones)");
                    if (data.getMoneyBoosterFromMilestone() != null && data.getMoneyBoosterFromMilestone() > 0) {
                        lore.add("§c| §fMoney Boosters: §c" + (data.getMoneyBoosterFromMilestone() * 10) + "%");
                    }
                    if (data.getTokenBoosterFromMilestone() != null && data.getTokenBoosterFromMilestone() > 0) {
                        lore.add("§c| §fTokens Boosters: §c" + (data.getTokenBoosterFromMilestone() * 10) + "%");
                    }
                    if (data.getKeysBoosterFromMilestone() != null && data.getKeysBoosterFromMilestone() > 0) {
                        lore.add("§c| §fKeys Boosters: §c+" + data.getKeysBoosterFromMilestone());
                    }
                    if (data.getXpBoosterFromMilestone() != null && data.getXpBoosterFromMilestone() > 0) {
                        lore.add("§c| §fXP Boosters: §c" + (data.getXpBoosterFromMilestone() * 10) + "%");
                    }
                    lore.add("");
                } else {
                    lore.add("§c§lMILESTONES §7§o(/milestones)");
                    lore.add("§c| §fYou don't have any booster from /milestones");
                    lore.add("");
                }
                // PETS
                List<PetBoost> petBoostsList = getPetBoosters(player);
                if (!petBoostsList.isEmpty()) {
                    lore.add("§d§lPETS");
                    for (PetBoost boost : petBoostsList) {
                        lore.add("§d| §f" + boost.getType() + ": §d" + boost.getValue());
                    }
                    lore.add("");
                } else {
                    lore.add("§d§lPETS");
                    lore.add("§d| §fYou don't have any pet activated");
                    lore.add("");
                }
                // KEYS BOOSTERS
                lore.add("§3§lKEYS");
                lore.add("§3| §fMoney Booster: §3" + (data.getKeysMoneyBooster() == null ? 0 : mainClass.newFormatNumber(data.getKeysMoneyBooster())) + "%");
                lore.add("§3| §fToken Booster: §3" + (data.getKeysTokenBooster() == null ? 0 : mainClass.newFormatNumber(data.getKeysTokenBooster())) + "%");
                lore.add("");
                // OTHER
                boolean atLeastOneBooster = false;
                lore.add("§7§lTEMPORARY BOOSTERS");
                User user = mainClass.getLuckPerms().getPlayerAdapter(Player.class).getUser(player);
                for (Node perm : user.getDistinctNodes()) {
                    if ((!perm.getKey().contains("temp")) && (perm.getKey().contains("autosell.multiplier") || (perm.getKey().contains("tokengreed.multiplier") || (perm.getKey().contains("nuke.multiplier"))))) {
                        String[] oldBooster = perm.getKey().split("\\.");
                        String type = oldBooster[0];
                        String tmptype = type.substring(0, 1).toUpperCase();
                        int value = Integer.parseInt(oldBooster[2]);
                        lore.add("§7| §f" + tmptype + type.substring(1) + " Multiplier: §7" + value + " (§7§o" + mainClass.formatDuration(Objects.requireNonNull(perm.getExpiryDuration())) + " left)");
                        if (!atLeastOneBooster) {
                            atLeastOneBooster = true;
                        }
                    } else if (perm.getKey().equalsIgnoreCase("voteshop.tokenbooster")) {
                        lore.add("§7| §fToken Multiplier: §72x (§7§o" + mainClass.formatDuration(Objects.requireNonNull(perm.getExpiryDuration())) + " left)");
                        if (!atLeastOneBooster) {
                            atLeastOneBooster = true;
                        }
                    } else if (perm.getKey().equalsIgnoreCase("voteshop.moneybooster")) {
                        lore.add("§7| §fMoney Multiplier: §72x (§7§o" + mainClass.formatDuration(Objects.requireNonNull(perm.getExpiryDuration())) + " left)");
                        if (!atLeastOneBooster) {
                            atLeastOneBooster = true;
                        }
                    }
                }
                if (!atLeastOneBooster) {
                    lore.add("§7| §fNo temporary booster activated");
                }
                meta.setLore(lore);
                boosters.setItemMeta(meta);

                gui.setItem(13, boosters);

                player.openInventory(gui);
            }
        }.runTask(mainClass);
    }

    public List<PetBoost> getPetBoosters(Player player) {
        String tmp;
        List<PetBoost> petBoosts = new ArrayList<>();

        if (player.getInventory().getHelmet() != null && player.getInventory().getHelmet().getType() != null && player.getInventory().getHelmet().hasItemMeta() && player.getInventory().getHelmet().getItemMeta().hasLore() && player.getInventory().getHelmet().getItemMeta().hasDisplayName()) {
            if (!player.getWorld().getName().equals("island_normal_world") && !player.getWorld().getName().equals("island_nether_world") && !player.getWorld().getName().equals("island_end_world")) {
                if (player.getInventory().getHelmet().getItemMeta().getDisplayName().equals("§a§lXP§7-Pet")) {
                    for (String line : player.getInventory().getHelmet().getItemMeta().getLore()) {
                        if (line.contains("XP BOOSTER")) {
                            tmp = ChatColor.stripColor(line);
                            tmp = tmp.replace("XP BOOSTER » ", "");
                            tmp = tmp.replace("%", "");
                            PetBoost boost = new PetBoost();
                            boost.setType("XP Booster");
                            boost.setValue(Double.parseDouble(tmp));
                            petBoosts.add(boost);
                        }
                    }
                } else if (player.getInventory().getHelmet().getItemMeta().getDisplayName().equals("§b§lBEACON§7-Pet")) {
                    for (String line : player.getInventory().getHelmet().getItemMeta().getLore()) {
                        if (line.contains("CHANCE")) {
                            tmp = ChatColor.stripColor(line);
                            tmp = tmp.replace("CHANCE » ", "");
                            tmp = tmp.replace("%", "");
                            PetBoost boost = new PetBoost();
                            boost.setType("Beacon Chance");
                            boost.setValue(Double.parseDouble(tmp));
                            petBoosts.add(boost);
                        }
                    }
                } else if (player.getInventory().getHelmet().getItemMeta().getDisplayName().equals("§a§lTOKEN§7-Pet")) {
                    for (String line : player.getInventory().getHelmet().getItemMeta().getLore()) {
                        if (line.contains("TOKEN GREED BOOST")) {
                            tmp = ChatColor.stripColor(line);
                            tmp = tmp.replace("TOKEN GREED BOOST » ", "");
                            tmp = tmp.replace("%", "");
                            PetBoost boost = new PetBoost();
                            boost.setType("Token Booster");
                            boost.setValue(Double.parseDouble(tmp));
                            petBoosts.add(boost);
                        } else if (line.contains("NUKE BOOST")) {
                            tmp = ChatColor.stripColor(line);
                            tmp = tmp.replace("NUKE BOOST » ", "");
                            tmp = tmp.replace("%", "");
                            PetBoost boost = new PetBoost();
                            boost.setType("Nuke Booster");
                            boost.setValue(Double.parseDouble(tmp));
                            petBoosts.add(boost);
                        }
                    }
                } else if (player.getInventory().getHelmet().getItemMeta().getDisplayName().equals("§e§lMONEY§7-Pet")) {
                    for (String line : player.getInventory().getHelmet().getItemMeta().getLore()) {
                        if (line.contains("AUTOSELL BOOST")) {
                            tmp = ChatColor.stripColor(line);
                            tmp = tmp.replace("AUTOSELL BOOST » ", "");
                            tmp = tmp.replace("%", "");
                            PetBoost boost = new PetBoost();
                            boost.setType("AutoSell Booster");
                            boost.setValue(Double.parseDouble(tmp));
                            petBoosts.add(boost);
                        } else if (line.contains("JACKHAMMER BOOST")) {
                            tmp = ChatColor.stripColor(line);
                            tmp = tmp.replace("JACKHAMMER BOOST » ", "");
                            tmp = tmp.replace("%", "");
                            PetBoost boost = new PetBoost();
                            boost.setType("JackHammer Booster");
                            boost.setValue(Double.parseDouble(tmp));
                            petBoosts.add(boost);
                        } else if (line.contains("NUKE BOOST")) {
                            tmp = ChatColor.stripColor(line);
                            tmp = tmp.replace("NUKE BOOST » ", "");
                            tmp = tmp.replace("%", "");
                            PetBoost boost = new PetBoost();
                            boost.setType("Nuke Booster");
                            boost.setValue(Double.parseDouble(tmp));
                            petBoosts.add(boost);
                        }
                    }
                } else if (player.getInventory().getHelmet().getItemMeta().getDisplayName().equals("§e§lKEY§7-Pet")) {
                    for (String line : player.getInventory().getHelmet().getItemMeta().getLore()) {
                        if (line.contains("EXTRA KEYS")) {
                            tmp = ChatColor.stripColor(line);
                            tmp = tmp.replace("EXTRA KEYS » ", "");
                            PetBoost boost = new PetBoost();
                            boost.setType("Keys Booster");
                            boost.setValue(Double.parseDouble(tmp));
                            petBoosts.add(boost);
                        }
                    }
                } else if (player.getInventory().getHelmet().getItemMeta().getDisplayName().equals("§6§lJACKHAMMER§7-Pet")) {
                    for (String line : player.getInventory().getHelmet().getItemMeta().getLore()) {
                        if (line.contains("JACKHAMMER BOOST")) {
                            tmp = ChatColor.stripColor(line);
                            tmp = tmp.replace("JACKHAMMER BOOST » ", "");
                            tmp = tmp.replace("%", "");
                            PetBoost boost = new PetBoost();
                            boost.setType("JackHammer Booster");
                            boost.setValue(Double.parseDouble(tmp));
                            petBoosts.add(boost);
                        }
                    }
                } else if (player.getInventory().getHelmet().getItemMeta().getDisplayName().equals("§c§lNUKE§7-Pet")) {
                    for (String line : player.getInventory().getHelmet().getItemMeta().getLore()) {
                        if (line.contains("NUKE BOOST")) {
                            tmp = ChatColor.stripColor(line);
                            tmp = tmp.replace("NUKE BOOST » ", "");
                            tmp = tmp.replace("%", "");
                            PetBoost boost = new PetBoost();
                            boost.setType("Nuke Booster");
                            boost.setValue(Double.parseDouble(tmp));
                            petBoosts.add(boost);
                        }
                    }
                } else if (player.getInventory().getHelmet().getItemMeta().getDisplayName().equals("§e§lWONDERLAND§7-Pet")) {
                    for (String line : player.getInventory().getHelmet().getItemMeta().getLore()) {
                        if (line.contains("WONDERLAND BOOST")) {
                            tmp = ChatColor.stripColor(line);
                            tmp = tmp.replace("WONDERLAND BOOST » ", "");
                            tmp = tmp.replace("%", "");
                            PetBoost boost = new PetBoost();
                            boost.setType("Wonderland Booster");
                            boost.setValue(Double.parseDouble(tmp));
                            petBoosts.add(boost);
                        }
                    }
                }
            }
        }

        return petBoosts;
    }
}

class PetBoost {

    private String type;
    private Double value;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Double getValue() {
        return value;
    }

    public void setValue(Double value) {
        this.value = value;
    }
}