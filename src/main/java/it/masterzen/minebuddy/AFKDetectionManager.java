package it.masterzen.minebuddy;

import it.masterzen.blockbreak.AlphaBlockBreak;
import it.masterzen.MongoDB.PlayerData;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.Date;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Manager class for the AFK detection system.
 * Coordinates location tracking, movement detection, and penalty application.
 */
public class AFKDetectionManager {
    
    private final AlphaBlockBreak plugin;
    private final AFKDetectionConfig config;
    private final AFKDetectionListener listener;
    private final String prefix = "§e§lMINE BUDDY §8»§7 ";
    
    // Location tracking data
    private final Map<UUID, LocationSnapshot> lastKnownLocations;
    private final Map<UUID, Long> lastMovementTimes;
    private final Map<UUID, Integer> stationaryCheckCounts;
    
    // Scheduled tasks
    private BukkitTask locationCheckTask;
    private BukkitTask penaltyUpdateTask;
    
    public AFKDetectionManager(AlphaBlockBreak plugin, AFKDetectionConfig config) {
        this.plugin = plugin;
        this.config = config;
        this.listener = new AFKDetectionListener(plugin, config);
        this.lastKnownLocations = new ConcurrentHashMap<>();
        this.lastMovementTimes = new ConcurrentHashMap<>();
        this.stationaryCheckCounts = new ConcurrentHashMap<>();
        
        // Register event listener
        plugin.getServer().getPluginManager().registerEvents(listener, plugin);
        
        // Start scheduled tasks
        startLocationCheckTask();
        startPenaltyUpdateTask();
    }
    
    /**
     * Checks if a player is eligible for MineBuddy based on AFK detection
     */
    public boolean isPlayerEligible(Player player, MineBuddy.LocationType locationType) {
        if (!config.isEnabledForLocation(locationType)) {
            return true; // AFK detection disabled for this location
        }
        
        UUID playerId = player.getUniqueId();
        PlayerActivityTracker tracker = listener.getTracker(playerId);
        
        if (tracker == null) {
            // No tracker yet, create one and allow activity
            tracker = listener.getOrCreateTracker(player);
            return true;
        }
        
        // Check if player is AFK
        boolean isAFK = tracker.isPlayerAFK(config);
        
        if (isAFK) {
            handleAFKPlayer(player, tracker);
            return false;
        }
        
        // Check for suspicious patterns if enabled
        if (config.isPatternDetectionEnabled() && tracker.hasSuspiciousPattern()) {
            handleSuspiciousPlayer(player, tracker);
            return false;
        }
        
        return true;
    }
    
    /**
     * Gets the penalty multiplier for a player (used to reduce MineBuddy efficiency)
     */
    public double getPlayerPenaltyMultiplier(Player player) {
        PlayerActivityTracker tracker = listener.getTracker(player.getUniqueId());
        if (tracker == null) {
            return 1.0; // No penalty
        }
        
        return tracker.getPenaltyMultiplier();
    }
    
    /**
     * Manually checks a player's location and updates tracking data
     */
    public void checkPlayerLocation(Player player) {
        UUID playerId = player.getUniqueId();
        Location currentLocation = player.getLocation();
        
        LocationSnapshot lastSnapshot = lastKnownLocations.get(playerId);
        long currentTime = System.currentTimeMillis();
        
        if (lastSnapshot != null) {
            double distance = calculateDistance(lastSnapshot.getLocation(), currentLocation);
            
            if (distance >= config.getMinMovementDistance()) {
                // Player has moved significantly
                lastMovementTimes.put(playerId, currentTime);
                stationaryCheckCounts.put(playerId, 0);
                
                // Update persistent data
                updatePlayerLocationData(player, currentLocation);
            } else {
                // Player hasn't moved much
                int stationaryCount = stationaryCheckCounts.getOrDefault(playerId, 0) + 1;
                stationaryCheckCounts.put(playerId, stationaryCount);
            }
        } else {
            // First time checking this player
            lastMovementTimes.put(playerId, currentTime);
            stationaryCheckCounts.put(playerId, 0);
        }
        
        // Update location snapshot
        lastKnownLocations.put(playerId, new LocationSnapshot(currentLocation, currentTime));
    }
    
    /**
     * Handles an AFK player by applying warnings and penalties
     */
    private void handleAFKPlayer(Player player, PlayerActivityTracker tracker) {
        if (!config.isPlayerNotifications()) {
            return; // Notifications disabled
        }
        
        long currentTime = System.currentTimeMillis();
        int warningCount = tracker.getWarningCount();
        
        // Check if enough time has passed since last warning
        if (currentTime - tracker.getLastMovementTime() < config.getWarningInterval()) {
            return;
        }
        
        if (warningCount < config.getMaxWarnings()) {
            // Send warning to player
            tracker.addWarning();
            sendAFKWarning(player, warningCount + 1);
            
            // Apply progressive penalty if enabled
            if (config.isProgressivePenalties()) {
                double penaltyMultiplier = 1.0 - (config.getPenaltyReductionRate() * (warningCount + 1));
                tracker.setPenaltyMultiplier(Math.max(0.0, penaltyMultiplier));
            }
            
            // Update persistent data
            updatePlayerWarningData(player, tracker);
        } else {
            // Maximum warnings reached, apply full penalty
            tracker.setPenaltyMultiplier(0.0);
            sendMaxWarningMessage(player);
        }
        
        // Notify admins if enabled
        if (config.isAdminNotifications()) {
            notifyAdmins(player, tracker);
        }
    }
    
    /**
     * Handles a player with suspicious movement patterns
     */
    private void handleSuspiciousPlayer(Player player, PlayerActivityTracker tracker) {
        // Apply immediate penalty for suspicious patterns
        double currentMultiplier = tracker.getPenaltyMultiplier();
        tracker.setPenaltyMultiplier(Math.max(0.0, currentMultiplier * 0.5)); // 50% reduction
        
        if (config.isPlayerNotifications()) {
            player.sendMessage(prefix + "§cSuspicious movement pattern detected. MineBuddy efficiency reduced.");
        }
        
        if (config.isAdminNotifications()) {
            String message = prefix + "§cSuspicious pattern detected for player " + player.getName();
            notifyAdminsWithMessage(message);
        }
        
        plugin.getLogger().warning("Suspicious movement pattern detected for player: " + player.getName());
    }
    
    /**
     * Sends AFK warning to player
     */
    private void sendAFKWarning(Player player, int warningNumber) {
        player.sendMessage(prefix + "§cAFK Warning " + warningNumber + "/" + config.getMaxWarnings());
        player.sendMessage(prefix + "§7Move around to avoid MineBuddy penalties!");
        
        // Send title for better visibility
        player.sendTitle("§c§lAFK WARNING", "§7Move to avoid penalties", 10, 60, 10);
    }
    
    /**
     * Sends maximum warning message to player
     */
    private void sendMaxWarningMessage(Player player) {
        player.sendMessage(prefix + "§cMaximum AFK warnings reached!");
        player.sendMessage(prefix + "§7MineBuddy has been disabled. Move around to reactivate.");
        player.sendTitle("§c§lAFK DETECTED", "§7MineBuddy disabled", 10, 80, 10);
    }
    
    /**
     * Notifies admins about AFK player
     */
    private void notifyAdmins(Player afkPlayer, PlayerActivityTracker tracker) {
        String message = prefix + "§eAFK detected: " + afkPlayer.getName() + 
                        " (Warnings: " + tracker.getWarningCount() + "/" + config.getMaxWarnings() + ")";
        notifyAdminsWithMessage(message);
    }
    
    /**
     * Sends a message to all online admins
     */
    private void notifyAdminsWithMessage(String message) {
        for (Player admin : Bukkit.getOnlinePlayers()) {
            if (admin.hasPermission("minebuddy.admin") || admin.isOp()) {
                admin.sendMessage(message);
            }
        }
    }
    
    /**
     * Updates persistent player location data
     */
    private void updatePlayerLocationData(Player player, Location location) {
        try {
            PlayerData playerData = plugin.getMongoReader().getPlayerData(player.getUniqueId());
            if (playerData != null) {
                playerData.setLastKnownLocationWorld(location.getWorld().getName());
                playerData.setLastKnownLocationX(location.getX());
                playerData.setLastKnownLocationY(location.getY());
                playerData.setLastKnownLocationZ(location.getZ());
                playerData.setLastMovementTime(new Date());
                
                // Save asynchronously
                new BukkitRunnable() {
                    @Override
                    public void run() {
                        plugin.getMongoReader().savePlayerData(playerData, false);
                    }
                }.runTaskAsynchronously(plugin);
            }
        } catch (Exception e) {
            plugin.getLogger().warning("Error updating player location data: " + e.getMessage());
        }
    }
    
    /**
     * Updates persistent player warning data
     */
    private void updatePlayerWarningData(Player player, PlayerActivityTracker tracker) {
        try {
            PlayerData playerData = plugin.getMongoReader().getPlayerData(player.getUniqueId());
            if (playerData != null) {
                playerData.setAfkWarningsCount(tracker.getWarningCount());
                playerData.setActivityScore(tracker.getCurrentActivityScore());
                
                // Save asynchronously
                new BukkitRunnable() {
                    @Override
                    public void run() {
                        plugin.getMongoReader().savePlayerData(playerData, false);
                    }
                }.runTaskAsynchronously(plugin);
            }
        } catch (Exception e) {
            plugin.getLogger().warning("Error updating player warning data: " + e.getMessage());
        }
    }
    
    /**
     * Calculates distance between two locations (ignoring Y coordinate for ground-level movement)
     */
    private double calculateDistance(Location loc1, Location loc2) {
        if (!loc1.getWorld().equals(loc2.getWorld())) {
            return Double.MAX_VALUE; // Different worlds = maximum distance
        }
        
        double deltaX = loc1.getX() - loc2.getX();
        double deltaZ = loc1.getZ() - loc2.getZ();
        return Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);
    }
    
    /**
     * Starts the location checking task
     */
    private void startLocationCheckTask() {
        locationCheckTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (!config.isEnabled()) return;
                
                for (Player player : Bukkit.getOnlinePlayers()) {
                    try {
                        checkPlayerLocation(player);
                    } catch (Exception e) {
                        plugin.getLogger().warning("Error checking location for " + player.getName() + ": " + e.getMessage());
                    }
                }
            }
        }.runTaskTimer(plugin, 100L, 100L); // Run every 5 seconds (same as MineBuddy)
    }
    
    /**
     * Starts the penalty update task
     */
    private void startPenaltyUpdateTask() {
        penaltyUpdateTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (!config.isEnabled()) return;
                
                // Update penalties and check for recovery
                for (Map.Entry<UUID, PlayerActivityTracker> entry : listener.getAllTrackers().entrySet()) {
                    Player player = Bukkit.getPlayer(entry.getKey());
                    if (player != null && player.isOnline()) {
                        PlayerActivityTracker tracker = entry.getValue();
                        
                        // Check if player has recovered from AFK state
                        if (tracker.getWarningCount() > 0 && !tracker.isPlayerAFK(config)) {
                            // Player is active again, reduce warnings
                            if (tracker.getWarningCount() > 0) {
                                tracker.resetWarnings();
                                tracker.setPenaltyMultiplier(1.0);
                                
                                if (config.isPlayerNotifications()) {
                                    player.sendMessage(prefix + "§aActivity detected! MineBuddy reactivated.");
                                }
                                
                                updatePlayerWarningData(player, tracker);
                            }
                        }
                    }
                }
            }
        }.runTaskTimer(plugin, 200L, 200L); // Run every 10 seconds
    }
    
    /**
     * Shuts down the AFK detection system
     */
    public void shutdown() {
        if (locationCheckTask != null) {
            locationCheckTask.cancel();
        }
        if (penaltyUpdateTask != null) {
            penaltyUpdateTask.cancel();
        }
        
        listener.shutdown();
        
        lastKnownLocations.clear();
        lastMovementTimes.clear();
        stationaryCheckCounts.clear();
    }
    
    /**
     * Gets the AFK detection listener
     */
    public AFKDetectionListener getListener() {
        return listener;
    }
    
    /**
     * Gets the configuration
     */
    public AFKDetectionConfig getConfig() {
        return config;
    }
    
    /**
     * Resets AFK data for a specific player
     */
    public void resetPlayerAFKData(UUID playerId) {
        lastKnownLocations.remove(playerId);
        lastMovementTimes.remove(playerId);
        stationaryCheckCounts.remove(playerId);
        listener.resetPlayerActivity(playerId);
    }
    
    /**
     * Gets AFK statistics for a player
     */
    public String getPlayerAFKStats(Player player) {
        UUID playerId = player.getUniqueId();
        PlayerActivityTracker tracker = listener.getTracker(playerId);
        
        if (tracker == null) {
            return "No AFK data available for " + player.getName();
        }
        
        StringBuilder stats = new StringBuilder();
        stats.append("AFK Stats for ").append(player.getName()).append(":\n");
        stats.append("  Activity Score: ").append(String.format("%.1f", tracker.getCurrentActivityScore())).append("/100\n");
        stats.append("  Warnings: ").append(tracker.getWarningCount()).append("/").append(config.getMaxWarnings()).append("\n");
        stats.append("  Penalty Multiplier: ").append(String.format("%.2f", tracker.getPenaltyMultiplier())).append("\n");
        stats.append("  Stationary Checks: ").append(tracker.getStationaryChecks()).append("\n");
        stats.append("  Suspicious Pattern: ").append(tracker.isSuspiciousPatternDetected() ? "Yes" : "No").append("\n");
        stats.append("  Last Movement: ").append((System.currentTimeMillis() - tracker.getLastMovementTime()) / 1000).append("s ago\n");
        stats.append("  Last Interaction: ").append((System.currentTimeMillis() - tracker.getLastInteractionTime()) / 1000).append("s ago");
        
        return stats.toString();
    }

    /**
     * Gets AFK statistics for a player (alias method)
     */
    public String getPlayerStats(Player player) {
        return getPlayerAFKStats(player);
    }

    // Inner class for location snapshots
    private static class LocationSnapshot {
        private final Location location;
        private final long timestamp;
        
        public LocationSnapshot(Location location, long timestamp) {
            this.location = location.clone();
            this.timestamp = timestamp;
        }
        
        public Location getLocation() { return location; }
        public long getTimestamp() { return timestamp; }
    }
}
